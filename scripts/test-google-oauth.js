/**
 * Test Google OAuth Configuration
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testGoogleOAuth() {
  console.log('🧪 Testing Google OAuth Configuration...\n')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables')
    return
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    console.log('📋 OAuth Configuration:')
    console.log(`   Supabase URL: ${supabaseUrl}`)
    console.log(`   Google Client ID: ${process.env.GOOGLE_CLIENT_ID}`)
    console.log(`   Admin Whitelist: ${process.env.ADMIN_EMAIL_WHITELIST}`)
    console.log('')
    
    console.log('🔗 OAuth URLs:')
    console.log(`   Login URL: http://localhost:3000/login`)
    console.log(`   Callback URL: http://localhost:3000/auth/callback`)
    console.log(`   Supabase Callback: ${supabaseUrl}/auth/v1/callback`)
    console.log('')
    
    console.log('📍 Google Console Configuration:')
    console.log('   Make sure these URIs are added to your Google OAuth app:')
    console.log('   - http://localhost:3000/auth/callback')
    console.log(`   - ${supabaseUrl}/auth/v1/callback`)
    console.log('')
    
    console.log('✅ Ready to test OAuth flow!')
    console.log('')
    console.log('📌 To fix OAuth, you need to:')
    console.log('1. Go to https://supabase.com/dashboard/project/cgzcndxnfldupgdddnra/auth/providers')
    console.log('2. Click on "Google" provider')
    console.log('3. Toggle it ON')
    console.log('4. Enter your Client ID and Client Secret')
    console.log('5. Save the configuration')
    console.log('')
    console.log('Then test at: http://localhost:3000/login')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testGoogleOAuth()