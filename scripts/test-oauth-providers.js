/**
 * Test OAuth Providers Configuration
 */

const { createClient } = require('@supabase/supabase-js')

async function testOAuthProviders() {
  console.log('🧪 Testing OAuth Provider Configuration...\n')
  
  const supabaseUrl = 'https://cgzcndxnfldupgdddnra.supabase.co'
  const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo'
  
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    // Generate OAuth URL to test if Google is enabled
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'http://localhost:3000/auth/callback',
        skipBrowserRedirect: true
      }
    })
    
    if (error) {
      console.log('❌ OAuth Error:', error.message)
      if (error.message.includes('not enabled')) {
        console.log('\n⚠️  Google OAuth is NOT enabled in your Supabase project!')
        console.log('\n📌 You need to:')
        console.log('1. Log in to Supabase with the account that owns this project')
        console.log('2. Go to: https://supabase.com/dashboard/project/cgzcndxnfldupgdddnra/auth/providers')
        console.log('3. Enable Google OAuth and add your credentials')
      }
    } else if (data && data.url) {
      console.log('✅ Google OAuth appears to be configured!')
      console.log('\n🔗 OAuth URL generated successfully')
      console.log(`   URL: ${data.url.substring(0, 100)}...`)
      console.log('\n✨ OAuth should be working! Try logging in at:')
      console.log('   http://localhost:3000/login')
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testOAuthProviders()