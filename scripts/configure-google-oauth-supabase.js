/**
 * Configure Google OAuth in Supabase
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function configureGoogleOAuth() {
  console.log('🔧 Configuring Google OAuth in Supabase...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  const googleClientId = process.env.GOOGLE_CLIENT_ID
  const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET
  
  if (!supabaseUrl || !serviceRoleKey || !googleClientId || !googleClientSecret) {
    console.error('❌ Missing required environment variables')
    console.log('Required:')
    console.log('- NEXT_PUBLIC_SUPABASE_URL')
    console.log('- SUPABASE_SERVICE_ROLE_KEY')
    console.log('- GOOGLE_CLIENT_ID')
    console.log('- GOOGLE_CLIENT_SECRET')
    return
  }

  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    console.log('✅ Admin client created')
    console.log('📋 Configuration details:')
    console.log(`   Supabase URL: ${supabaseUrl}`)
    console.log(`   Google Client ID: ${googleClientId}`)
    console.log(`   Redirect URI: ${supabaseUrl}/auth/v1/callback`)

    // Note: The actual OAuth provider configuration needs to be done through the dashboard
    // This script verifies the credentials are ready
    
    console.log('')
    console.log('🎯 Next steps:')
    console.log('1. Go to your Supabase dashboard: https://supabase.com/dashboard')
    console.log('2. Navigate to Authentication → Providers')
    console.log('3. Enable Google provider with these credentials:')
    console.log(`   Client ID: ${googleClientId}`)
    console.log(`   Client Secret: ${googleClientSecret}`)
    console.log('4. Ensure redirect URL is set to:')
    console.log(`   ${supabaseUrl}/auth/v1/callback`)
    console.log('')
    console.log('✅ All credentials are ready for configuration!')

  } catch (error) {
    console.error('❌ Configuration failed:', error.message)
  }
}

configureGoogleOAuth()
