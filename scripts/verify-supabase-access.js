/**
 * Verify Supabase Project Access
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function verifySupabaseAccess() {
  console.log('🔍 Verifying Supabase Project Access...\n')
  
  const supabaseUrl = 'https://cgzcndxnfldupgdddnra.supabase.co'
  const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnemNuZHhuZmxkdXBnZGRkbnJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MjU3OTUsImV4cCI6MjA2ODEwMTc5NX0.XZR5Qlhp31SV2rn30rdhDNKd3x402pLWuma9E-Zy2Oo'
  
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    console.log('📋 Project Details:')
    console.log(`   URL: ${supabaseUrl}`)
    console.log(`   Project ID: cgzcndxnfldupgdddnra`)
    console.log('')
    
    // Test basic connection
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.log('❌ Session Error:', sessionError.message)
    } else {
      console.log('✅ Successfully connected to Supabase!')
      console.log(`   Session exists: ${sessionData.session ? 'Yes' : 'No'}`)
    }
    
    console.log('')
    console.log('🔗 Direct Dashboard Links:')
    console.log('   Project Dashboard: https://supabase.com/dashboard/project/cgzcndxnfldupgdddnra')
    console.log('   OAuth Providers: https://supabase.com/dashboard/project/cgzcndxnfldupgdddnra/auth/providers')
    console.log('')
    
    console.log('📌 To enable Google OAuth:')
    console.log('1. Click the OAuth Providers link above')
    console.log('2. If you get access denied, you need to:')
    console.log('   - Log in to Supabase with the account that owns this project')
    console.log('   - OR create a new project that you own')
    console.log('')
    console.log('3. Once in the dashboard:')
    console.log('   - Find "Google" in the providers list')
    console.log('   - Toggle it ON')
    console.log('   - Add your Client ID and Secret')
    console.log('   - Save the configuration')
    
    // Try to check if tables exist (this might fail due to RLS)
    const { data: tables, error: tablesError } = await supabase
      .from('AdminUser')
      .select('count')
      .limit(1)
    
    if (!tablesError) {
      console.log('\n✅ Database tables are accessible')
    } else if (tablesError.message.includes('relation') && tablesError.message.includes('does not exist')) {
      console.log('\n⚠️  Database tables may not be set up yet')
      console.log('   Run: npm run sync-db')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message)
  }
}

verifySupabaseAccess()