/**
 * Test Supabase Service Role Key
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function testServiceRole() {
  console.log('🔑 Testing Supabase Service Role Key...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('❌ Missing Supabase credentials')
    return
  }

  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    console.log('✅ Service role client created')

    // Test admin access
    const { data, error } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 5
    })

    if (error) {
      console.error('❌ Service role test failed:', error.message)
      return
    }

    console.log('✅ Service role key working!')
    console.log(`📊 Found ${data.users.length} users in auth system`)

    // Test database access
    const { data: tenants, error: dbError } = await supabase
      .from('Tenant')
      .select('id, name, slug')
      .limit(5)

    if (dbError) {
      console.error('❌ Database access failed:', dbError.message)
      return
    }

    console.log('✅ Database access working!')
    console.log(`📊 Found ${tenants.length} tenants in database`)

  } catch (error) {
    console.error('❌ Service role test failed:', error.message)
  }
}

testServiceRole()
