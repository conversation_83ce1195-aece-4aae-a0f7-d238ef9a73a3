'use client'

/**
 * Authentication Test Page
 * Test Google OAuth integration with Supabase
 */

import { useState, useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'
import GoogleAuthButton from '@/components/auth/GoogleAuthButton'

export default function TestLoginPage() {
  const [user, setUser] = useState<any>(null)
  const [session, setSession] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      if (error) {
        console.error('Error getting session:', error)
        setError(error.message)
      } else {
        setSession(session)
        setUser(session?.user || null)
      }
      setLoading(false)
    }

    getSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session)
        setSession(session)
        setUser(session?.user || null)
        setError(null)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  const handleSignOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) {
      setError(error.message)
    }
  }

  const handleAuthError = (errorMessage: string) => {
    setError(errorMessage)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Authentication Test
          </h1>
          <p className="text-gray-600">
            Test Google OAuth integration with Supabase
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex">
              <div className="text-red-400 text-xl mr-3">❌</div>
              <div>
                <h3 className="text-red-800 font-medium">Authentication Error</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          
          {user ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="text-green-500 text-2xl">✅</div>
                <div>
                  <h3 className="font-medium text-gray-900">Signed In</h3>
                  <p className="text-gray-600 text-sm">Welcome back!</p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">User Information</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Email:</strong> {user.email}</div>
                  <div><strong>Name:</strong> {user.user_metadata?.full_name || 'Not provided'}</div>
                  <div><strong>Provider:</strong> {user.app_metadata?.provider || 'Unknown'}</div>
                  <div><strong>User ID:</strong> {user.id}</div>
                  <div><strong>Last Sign In:</strong> {new Date(user.last_sign_in_at).toLocaleString()}</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Session Information</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Access Token:</strong> {session?.access_token ? '✅ Present' : '❌ Missing'}</div>
                  <div><strong>Refresh Token:</strong> {session?.refresh_token ? '✅ Present' : '❌ Missing'}</div>
                  <div><strong>Expires At:</strong> {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'Unknown'}</div>
                </div>
              </div>

              <button
                onClick={handleSignOut}
                className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Sign Out
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="text-gray-400 text-2xl">👤</div>
                <div>
                  <h3 className="font-medium text-gray-900">Not Signed In</h3>
                  <p className="text-gray-600 text-sm">Please sign in to test authentication</p>
                </div>
              </div>

              <GoogleAuthButton
                onError={handleAuthError}
                className="w-full"
              />
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Configuration Status</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Supabase URL:</span>
              <span className="text-green-600">✅ Configured</span>
            </div>
            <div className="flex justify-between">
              <span>Supabase Anon Key:</span>
              <span className="text-green-600">✅ Configured</span>
            </div>
            <div className="flex justify-between">
              <span>Google Client ID:</span>
              <span className="text-green-600">✅ Configured</span>
            </div>
            <div className="flex justify-between">
              <span>Google OAuth Provider:</span>
              <span className="text-yellow-600">⚠️ Check Supabase Dashboard</span>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  )
}
